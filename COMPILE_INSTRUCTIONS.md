# 🔨 GuoBiaoDietitian Android App 编译指南

## 📋 编译环境要求

### 必需软件
- **Android Studio**: Hedgehog (2023.1.1) 或更新版本
- **Java JDK**: 17 或更高版本
- **Android SDK**: API Level 34 (Android 14)
- **Gradle**: 8.0+ (通常由Android Studio管理)

### 推荐配置
- **内存**: 8GB+ RAM
- **存储**: 10GB+ 可用空间
- **网络**: 稳定的互联网连接（首次构建需要下载依赖）

## 🚀 编译步骤

### 方法一：使用Android Studio（推荐）

1. **打开项目**
   ```bash
   # 克隆项目
   git clone https://github.com/dom-liu/gbdietitian-android-app.git
   cd gbdietitian-android-app
   ```

2. **在Android Studio中打开**
   - 启动Android Studio
   - 选择 "Open an Existing Project"
   - 选择项目根目录

3. **同步项目**
   - Android Studio会自动提示同步Gradle
   - 点击 "Sync Now" 等待同步完成

4. **编译项目**
   - 菜单栏: Build → Make Project (Ctrl+F9)
   - 或点击工具栏的锤子图标

5. **生成APK**
   - 菜单栏: Build → Build Bundle(s) / APK(s) → Build APK(s)
   - APK文件位置: `app/build/outputs/apk/debug/app-debug.apk`

### 方法二：使用命令行

1. **设置环境变量**
   ```bash
   # 设置Android SDK路径（根据实际安装路径调整）
   export ANDROID_HOME=/path/to/android-sdk
   export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
   ```

2. **使用Gradle编译**
   ```bash
   # 清理项目
   ./gradlew clean
   
   # 编译Debug版本
   ./gradlew assembleDebug
   
   # 编译Release版本（需要签名配置）
   ./gradlew assembleRelease
   ```

3. **使用构建脚本**
   ```bash
   # 使用项目提供的构建脚本
   chmod +x build.sh
   ./build.sh
   ```

## 📱 APK输出位置

编译成功后，APK文件将生成在以下位置：

```
app/build/outputs/apk/
├── debug/
│   └── app-debug.apk          # Debug版本APK
└── release/
    └── app-release.apk        # Release版本APK（如果已配置签名）
```

## 🔧 常见编译问题及解决方案

### 1. Android SDK未找到
```
错误: SDK location not found
解决: 设置ANDROID_HOME环境变量或在项目根目录创建local.properties文件
```

**解决方案:**
```bash
# 创建local.properties文件
echo "sdk.dir=/path/to/android-sdk" > local.properties
```

### 2. Gradle版本不兼容
```
错误: Gradle version incompatible
解决: 更新gradle-wrapper.properties中的Gradle版本
```

### 3. 依赖下载失败
```
错误: Could not resolve dependencies
解决: 检查网络连接，或配置代理
```

**解决方案:**
```bash
# 在gradle.properties中配置代理（如需要）
systemProp.http.proxyHost=proxy.company.com
systemProp.http.proxyPort=8080
systemProp.https.proxyHost=proxy.company.com
systemProp.https.proxyPort=8080
```

### 4. 内存不足
```
错误: OutOfMemoryError
解决: 增加Gradle JVM内存
```

**解决方案:**
在`gradle.properties`中添加：
```properties
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=512m
```

### 5. Kotlin编译错误
```
错误: Kotlin compilation error
解决: 检查Kotlin版本兼容性
```

## 📊 编译性能优化

### 1. 启用Gradle并行编译
```properties
# gradle.properties
org.gradle.parallel=true
org.gradle.configureondemand=true
```

### 2. 启用增量编译
```properties
# gradle.properties
kotlin.incremental=true
kotlin.incremental.android=true
```

### 3. 使用Gradle缓存
```bash
# 启用构建缓存
./gradlew --build-cache assembleDebug
```

## 🧪 编译验证

编译完成后，可以通过以下方式验证APK：

### 1. 检查APK信息
```bash
# 使用aapt查看APK信息
aapt dump badging app/build/outputs/apk/debug/app-debug.apk

# 查看APK大小
ls -lh app/build/outputs/apk/debug/app-debug.apk
```

### 2. 安装到设备
```bash
# 安装到连接的设备或模拟器
adb install app/build/outputs/apk/debug/app-debug.apk

# 或使用Gradle任务
./gradlew installDebug
```

### 3. 运行应用
```bash
# 启动应用
adb shell am start -n com.borealbit.gbdietitian.android/.presentation.MainActivity
```

## 📈 构建统计

典型的编译时间和资源使用：

| 构建类型 | 首次编译 | 增量编译 | APK大小 |
|---------|---------|---------|---------|
| Debug   | 3-5分钟  | 30-60秒  | ~15MB   |
| Release | 5-8分钟  | 1-2分钟  | ~8MB    |

*注：时间可能因硬件配置和网络状况而异*

## 🎯 下一步

编译成功后，您可以：

1. **安装到设备**: 测试应用功能
2. **代码调试**: 使用Android Studio调试器
3. **性能分析**: 使用Android Profiler
4. **UI测试**: 运行Espresso测试
5. **发布准备**: 配置签名和混淆

## 📞 获取帮助

如果遇到编译问题，可以：

1. 查看Android Studio的Build输出窗口
2. 检查Gradle Console的详细错误信息
3. 参考项目的GitHub Issues
4. 联系开发团队：<EMAIL>

---

**祝您编译顺利！** 🎉
