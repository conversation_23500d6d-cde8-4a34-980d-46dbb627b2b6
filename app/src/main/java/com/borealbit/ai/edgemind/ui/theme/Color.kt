/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.borealbit.ai.edgemind.ui.theme

import androidx.compose.ui.graphics.Color

// EdgeMind Design Colors - Based on Figma Design
val primaryLight = Color(0xFF1F1F1F) // Dark background from design
val onPrimaryLight = Color(0xFFFFFFFF) // White text
val primaryContainerLight = Color(0xFF2A2A2A) // Slightly lighter dark
val onPrimaryContainerLight = Color(0xFFFFFFFF)
val secondaryLight = Color(0xFF61758A) // Muted blue-gray from design
val onSecondaryLight = Color(0xFFFFFFFF)
val secondaryContainerLight = Color(0xFF3A3A3A) // Card background
val onSecondaryContainerLight = Color(0xFFFFFFFF)
val tertiaryLight = Color(0xFF775A0B)
val onTertiaryLight = Color(0xFFFFFFFF)
val tertiaryContainerLight = Color(0xFFFFDF9B)
val onTertiaryContainerLight = Color(0xFF5B4300)
val errorLight = Color(0xFF904A43)
val onErrorLight = Color(0xFFFFFFFF)
val errorContainerLight = Color(0xFFFFDAD5)
val onErrorContainerLight = Color(0xFF73342D)
val backgroundLight = Color(0xFF1F1F1F) // Dark background matching design
val onBackgroundLight = Color(0xFFFFFFFF) // White text on dark background
val surfaceLight = Color(0xFF1F1F1F) // Dark surface
val onSurfaceLight = Color(0xFFFFFFFF) // White text on dark surface
val surfaceVariantLight = Color(0xFFDEE3EB)
val onSurfaceVariantLight = Color(0xFF42474E)
val outlineLight = Color(0xFF73777F)
val outlineVariantLight = Color(0xFFC2C7CF)
val scrimLight = Color(0xFF000000)
val inverseSurfaceLight = Color(0xFF2D3135)
val inverseOnSurfaceLight = Color(0xFFEFF1F6)
val inversePrimaryLight = Color(0xFF9DCAFC)
val surfaceDimLight = Color(0xFFD8DAE0)
val surfaceBrightLight = Color(0xFFF8F9FF)
val surfaceContainerLowestLight = Color(0xFFFFFFFF)
val surfaceContainerLowLight = Color(0xFFF2F3F9)
val surfaceContainerLight = Color(0xFFECEEF4)
val surfaceContainerHighLight = Color(0xFFE6E8EE)
val surfaceContainerHighestLight = Color(0xFFE0E2E8)

val primaryDark = Color(0xFF9DCAFC)
val onPrimaryDark = Color(0xFF003355)
val primaryContainerDark = Color(0xFF144A74)
val onPrimaryContainerDark = Color(0xFFD0E4FF)
val secondaryDark = Color(0xFFBAC8DA)
val onSecondaryDark = Color(0xFF243240)
val secondaryContainerDark = Color(0xFF3B4857)
val onSecondaryContainerDark = Color(0xFFD6E4F7)
val tertiaryDark = Color(0xFFE8C26C)
val onTertiaryDark = Color(0xFF3F2E00)
val tertiaryContainerDark = Color(0xFF5B4300)
val onTertiaryContainerDark = Color(0xFFFFDF9B)
val errorDark = Color(0xFFFFB4AB)
val onErrorDark = Color(0xFF561E19)
val errorContainerDark = Color(0xFF73342D)
val onErrorContainerDark = Color(0xFFFFDAD5)
val backgroundDark = Color(0xFF101418)
val onBackgroundDark = Color(0xFFE0E2E8)
val surfaceDark = Color(0xFF101418)
val onSurfaceDark = Color(0xFFE0E2E8)
val surfaceVariantDark = Color(0xFF42474E)
val onSurfaceVariantDark = Color(0xFFC2C7CF)
val outlineDark = Color(0xFF8C9199)
val outlineVariantDark = Color(0xFF42474E)
val scrimDark = Color(0xFF000000)
val inverseSurfaceDark = Color(0xFFE0E2E8)
val inverseOnSurfaceDark = Color(0xFF2D3135)
val inversePrimaryDark = Color(0xFF32628D)
val surfaceDimDark = Color(0xFF101418)
val surfaceBrightDark = Color(0xFF36393E)
val surfaceContainerLowestDark = Color(0xFF0B0E12)
val surfaceContainerLowDark = Color(0xFF191C20)
val surfaceContainerDark = Color(0xFF1D2024)
val surfaceContainerHighDark = Color(0xFF272A2F)
val surfaceContainerHighestDark = Color(0xFF32353A)
