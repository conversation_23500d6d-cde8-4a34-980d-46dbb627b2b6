/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.borealbit.ai.edgemind.ui.preview

import com.borealbit.ai.edgemind.data.AccessTokenData
import com.borealbit.ai.edgemind.data.DataStoreRepository
import com.borealbit.ai.edgemind.data.ImportedModelInfo

class PreviewDataStoreRepository : DataStoreRepository {
  override fun saveTextInputHistory(history: List<String>) {
  }

  override fun readTextInputHistory(): List<String> {
    return listOf()
  }

  override fun saveThemeOverride(theme: String) {
  }

  override fun readThemeOverride(): String {
    return ""
  }

  override fun saveAccessTokenData(accessToken: String, refreshToken: String, expiresAt: Long) {
  }

  override fun readAccessTokenData(): AccessTokenData? {
    return null
  }

  override fun clearAccessTokenData() {
  }

  override fun saveImportedModels(importedModels: List<ImportedModelInfo>) {
  }

  override fun readImportedModels(): List<ImportedModelInfo> {
    return listOf()
  }
}