/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.borealbit.ai.edgemind.ui.llmchat

import android.graphics.Bitmap
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import com.borealbit.ai.edgemind.ui.ViewModelProvider
import com.borealbit.ai.edgemind.ui.common.chat.ChatMessageImage
import com.borealbit.ai.edgemind.ui.common.chat.ChatMessageText
import com.borealbit.ai.edgemind.ui.common.chat.ChatView
import com.borealbit.ai.edgemind.ui.modelmanager.ModelManagerViewModel
import kotlinx.serialization.Serializable

/** Navigation destination data */
object LlmChatDestination {
  @Serializable
  val route = "LlmChatRoute"
}

object LlmAskImageDestination {
  @Serializable
  val route = "LlmAskImageRoute"
}

@Composable
fun LlmChatScreen(
  modelManagerViewModel: ModelManagerViewModel,
  navigateUp: () -> Unit,
  modifier: Modifier = Modifier,
  viewModel: LlmChatViewModel = viewModel(
    factory = ViewModelProvider.Factory
  ),
) {
  ChatViewWrapper(
    viewModel = viewModel,
    modelManagerViewModel = modelManagerViewModel,
    navigateUp = navigateUp,
    modifier = modifier,
  )
}

@Composable
fun LlmAskImageScreen(
  modelManagerViewModel: ModelManagerViewModel,
  navigateUp: () -> Unit,
  modifier: Modifier = Modifier,
  viewModel: LlmAskImageViewModel = viewModel(
    factory = ViewModelProvider.Factory
  ),
) {
  ChatViewWrapper(
    viewModel = viewModel,
    modelManagerViewModel = modelManagerViewModel,
    navigateUp = navigateUp,
    modifier = modifier,
  )
}

@Composable
fun ChatViewWrapper(
  viewModel: LlmChatViewModel,
  modelManagerViewModel: ModelManagerViewModel,
  navigateUp: () -> Unit,
  modifier: Modifier = Modifier
) {
  val context = LocalContext.current

  ChatView(
    task = viewModel.task,
    viewModel = viewModel,
    modelManagerViewModel = modelManagerViewModel,
    onSendMessage = { model, messages ->
      for (message in messages) {
        viewModel.addMessage(
          model = model,
          message = message,
        )
      }

      var text = ""
      var image: Bitmap? = null
      var chatMessageText: ChatMessageText? = null
      for (message in messages) {
        if (message is ChatMessageText) {
          chatMessageText = message
          text = message.content
        } else if (message is ChatMessageImage) {
          image = message.bitmap
        }
      }
      if (text.isNotEmpty() && chatMessageText != null) {
        modelManagerViewModel.addTextInputHistory(text)
        viewModel.generateResponse(model = model, input = text, image = image, onError = {
          viewModel.handleError(
            context = context,
            model = model,
            modelManagerViewModel = modelManagerViewModel,
            triggeredMessage = chatMessageText,
          )
        })
      }
    },
    onRunAgainClicked = { model, message ->
      if (message is ChatMessageText) {
        viewModel.runAgain(model = model, message = message, onError = {
          viewModel.handleError(
            context = context,
            model = model,
            modelManagerViewModel = modelManagerViewModel,
            triggeredMessage = message,
          )
        })
      }
    },
    onBenchmarkClicked = { _, _, _, _ ->
    },
    onResetSessionClicked = { model ->
      viewModel.resetSession(model = model)
    },
    showStopButtonInInputWhenInProgress = true,
    onStopButtonClicked = { model ->
      viewModel.stopResponse(model = model)
    },
    navigateUp = navigateUp,
    modifier = modifier,
  )
}