/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.borealbit.ai.edgemind.ui.modelmanager

import android.content.Context
import android.net.Uri
import android.util.Log
import androidx.activity.result.ActivityResult
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.borealbit.ai.edgemind.data.AGWorkInfo
import com.borealbit.ai.edgemind.data.Accelerator
import com.borealbit.ai.edgemind.data.AccessTokenData
import com.borealbit.ai.edgemind.data.Config
import com.borealbit.ai.edgemind.data.ConfigKey
import com.borealbit.ai.edgemind.data.DataStoreRepository
import com.borealbit.ai.edgemind.data.DownloadRepository
import com.borealbit.ai.edgemind.data.EMPTY_MODEL
import com.borealbit.ai.edgemind.data.IMPORTS_DIR
import com.borealbit.ai.edgemind.data.ImportedModelInfo
import com.borealbit.ai.edgemind.data.Model
import com.borealbit.ai.edgemind.data.ModelAllowlist
import com.borealbit.ai.edgemind.data.ModelDownloadStatus
import com.borealbit.ai.edgemind.data.ModelDownloadStatusType
import com.borealbit.ai.edgemind.data.TASKS
import com.borealbit.ai.edgemind.data.TASK_LLM_ASK_IMAGE
import com.borealbit.ai.edgemind.data.TASK_LLM_CHAT
import com.borealbit.ai.edgemind.data.TASK_LLM_PROMPT_LAB
import com.borealbit.ai.edgemind.data.Task
import com.borealbit.ai.edgemind.data.TaskType
import com.borealbit.ai.edgemind.data.ValueType
import com.borealbit.ai.edgemind.data.getModelByName
import com.borealbit.ai.edgemind.ui.common.AuthConfig
import com.borealbit.ai.edgemind.ui.common.convertValueToTargetType
import com.borealbit.ai.edgemind.ui.common.getJsonResponse
import com.borealbit.ai.edgemind.ui.common.processTasks
import com.borealbit.ai.edgemind.ui.imageclassification.ImageClassificationModelHelper
import com.borealbit.ai.edgemind.ui.imagegeneration.ImageGenerationModelHelper
import com.borealbit.ai.edgemind.ui.llmchat.LlmChatModelHelper
import com.borealbit.ai.edgemind.ui.llmchat.createLlmChatConfigs
import com.borealbit.ai.edgemind.ui.textclassification.TextClassificationModelHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.Json
import net.openid.appauth.AuthorizationException
import net.openid.appauth.AuthorizationRequest
import net.openid.appauth.AuthorizationResponse
import net.openid.appauth.AuthorizationService
import net.openid.appauth.ResponseTypeValues
import java.io.File
import java.net.HttpURLConnection
import java.net.URL

private const val TAG = "AGModelManagerViewModel"
private const val TEXT_INPUT_HISTORY_MAX_SIZE = 50
private const val MODEL_ALLOWLIST_URL =
  "https://raw.githubusercontent.com/google-ai-edge/gallery/refs/heads/main/model_allowlist.json"
private const val MODEL_ALLOWLIST_FILENAME = "model_allowlist.json"

data class ModelInitializationStatus(
  val status: ModelInitializationStatusType, var error: String = ""
)

enum class ModelInitializationStatusType {
  NOT_INITIALIZED, INITIALIZING, INITIALIZED, ERROR
}

enum class TokenStatus {
  NOT_STORED, EXPIRED, NOT_EXPIRED,
}

enum class TokenRequestResultType {
  FAILED, SUCCEEDED, USER_CANCELLED
}

data class TokenStatusAndData(
  val status: TokenStatus,
  val data: AccessTokenData?,
)

data class TokenRequestResult(
  val status: TokenRequestResultType, val errorMessage: String? = null
)

data class ModelManagerUiState(
  /**
   * A list of tasks available in the application.
   */
  val tasks: List<Task>,

  /**
   * A map that tracks the download status of each model, indexed by model name.
   */
  val modelDownloadStatus: Map<String, ModelDownloadStatus>,

  /**
   * A map that tracks the initialization status of each model, indexed by model name.
   */
  val modelInitializationStatus: Map<String, ModelInitializationStatus>,

  /**
   * Whether the app is loading and processing the model allowlist.
   */
  val loadingModelAllowlist: Boolean = true,

  /** The error message when loading the model allowlist. */
  val loadingModelAllowlistError: String = "",

  /**
   * The currently selected model.
   */
  val selectedModel: Model = EMPTY_MODEL,

  /**
   * The history of text inputs entered by the user.
   */
  val textInputHistory: List<String> = listOf(),
)

data class PagerScrollState(
  val page: Int = 0,
  val offset: Float = 0f,
)

/**
 * ViewModel responsible for managing models, their download status, and initialization.
 *
 * This ViewModel handles model-related operations such as downloading, deleting, initializing,
 * and cleaning up models. It also manages the UI state for model management, including the
 * list of tasks, models, download statuses, and initialization statuses.
 */
@OptIn(ExperimentalSerializationApi::class)
open class ModelManagerViewModel(
  private val downloadRepository: DownloadRepository,
  private val dataStoreRepository: DataStoreRepository,
  context: Context,
) : ViewModel() {
  private val externalFilesDir = context.getExternalFilesDir(null)
  private val inProgressWorkInfos: List<AGWorkInfo> =
    downloadRepository.getEnqueuedOrRunningWorkInfos()
  protected val _uiState = MutableStateFlow(createEmptyUiState())
  val uiState = _uiState.asStateFlow()

  val authService = AuthorizationService(context)
  var curAccessToken: String = ""

  var pagerScrollState: MutableStateFlow<PagerScrollState> = MutableStateFlow(PagerScrollState())

  init {
    loadModelAllowlist()
  }

  override fun onCleared() {
    super.onCleared()
    authService.dispose()
  }

  fun selectModel(model: Model) {
    _uiState.update { _uiState.value.copy(selectedModel = model) }
  }

  fun downloadModel(task: Task, model: Model) {
    // Update status.
    setDownloadStatus(
      curModel = model, status = ModelDownloadStatus(status = ModelDownloadStatusType.IN_PROGRESS)
    )

    // Delete the model files first.
    deleteModel(task = task, model = model)

    // Start to send download request.
    downloadRepository.downloadModel(
      model, onStatusUpdated = this::setDownloadStatus
    )
  }

  fun cancelDownloadModel(task: Task, model: Model) {
    downloadRepository.cancelDownloadModel(model)
    deleteModel(task = task, model = model)
  }

  fun deleteModel(task: Task, model: Model) {
    if (model.imported) {
      deleteFileFromExternalFilesDir(model.downloadFileName)
    } else {
      deleteDirFromExternalFilesDir(model.normalizedName)
    }

    // Update model download status to NotDownloaded.
    val curModelDownloadStatus = uiState.value.modelDownloadStatus.toMutableMap()
    curModelDownloadStatus[model.name] =
      ModelDownloadStatus(status = ModelDownloadStatusType.NOT_DOWNLOADED)

    // Delete model from the list if model is imported as a local model.
    if (model.imported) {
      for (curTask in TASKS) {
        val index = curTask.models.indexOf(model)
        if (index >= 0) {
          curTask.models.removeAt(index)
        }
        curTask.updateTrigger.value = System.currentTimeMillis()
      }
      curModelDownloadStatus.remove(model.name)

      // Update preference.
      val importedModels = dataStoreRepository.readImportedModels().toMutableList()
      val importedModelIndex = importedModels.indexOfFirst { it.fileName == model.name }
      if (importedModelIndex >= 0) {
        importedModels.removeAt(importedModelIndex)
      }
      dataStoreRepository.saveImportedModels(importedModels = importedModels)
    }
    val newUiState = uiState.value.copy(
      modelDownloadStatus = curModelDownloadStatus, tasks = uiState.value.tasks.toList()
    )
    _uiState.update { newUiState }
  }

  fun initializeModel(context: Context, task: Task, model: Model, force: Boolean = false) {
    viewModelScope.launch(Dispatchers.Default) {
      // Skip if initialized already.
      if (!force && uiState.value.modelInitializationStatus[model.name]?.status == ModelInitializationStatusType.INITIALIZED) {
        Log.d(TAG, "Model '${model.name}' has been initialized. Skipping.")
        return@launch
      }

      // Skip if initialization is in progress.
      if (model.initializing) {
        model.cleanUpAfterInit = false
        Log.d(TAG, "Model '${model.name}' is being initialized. Skipping.")
        return@launch
      }

      // Clean up.
      cleanupModel(task = task, model = model)

      // Start initialization.
      Log.d(TAG, "Initializing model '${model.name}'...")
      model.initializing = true

      // Show initializing status after a delay. When the delay expires, check if the model has
      // been initialized or not. If so, skip.
      launch {
        delay(500)
        if (model.instance == null && model.initializing) {
          updateModelInitializationStatus(
            model = model, status = ModelInitializationStatusType.INITIALIZING
          )
        }
      }

      val onDone: (error: String) -> Unit = { error ->
        model.initializing = false
        if (model.instance != null) {
          Log.d(TAG, "Model '${model.name}' initialized successfully")
          updateModelInitializationStatus(
            model = model,
            status = ModelInitializationStatusType.INITIALIZED,
          )
          if (model.cleanUpAfterInit) {
            Log.d(TAG, "Model '${model.name}' needs cleaning up after init.")
            cleanupModel(task = task, model = model)
          }
        } else if (error.isNotEmpty()) {
          Log.d(TAG, "Model '${model.name}' failed to initialize")
          updateModelInitializationStatus(
            model = model,
            status = ModelInitializationStatusType.ERROR,
            error = error,
          )
        }
      }
      when (task.type) {
        TaskType.TEXT_CLASSIFICATION -> TextClassificationModelHelper.initialize(
          context = context,
          model = model,
          onDone = onDone,
        )

        TaskType.IMAGE_CLASSIFICATION -> ImageClassificationModelHelper.initialize(
          context = context,
          model = model,
          onDone = onDone,
        )

        TaskType.LLM_CHAT -> LlmChatModelHelper.initialize(
          context = context,
          model = model,
          onDone = onDone,
        )

        TaskType.LLM_PROMPT_LAB -> LlmChatModelHelper.initialize(
          context = context,
          model = model,
          onDone = onDone,
        )

        TaskType.LLM_ASK_IMAGE -> LlmChatModelHelper.initialize(
          context = context,
          model = model,
          onDone = onDone,
        )

        TaskType.IMAGE_GENERATION -> ImageGenerationModelHelper.initialize(
          context = context, model = model, onDone = onDone
        )

        TaskType.TEST_TASK_1 -> {}
        TaskType.TEST_TASK_2 -> {}
      }
    }
  }

  fun cleanupModel(task: Task, model: Model) {
    if (model.instance != null) {
      model.cleanUpAfterInit = false
      Log.d(TAG, "Cleaning up model '${model.name}'...")
      when (task.type) {
        TaskType.TEXT_CLASSIFICATION -> TextClassificationModelHelper.cleanUp(model = model)
        TaskType.IMAGE_CLASSIFICATION -> ImageClassificationModelHelper.cleanUp(model = model)
        TaskType.LLM_CHAT -> LlmChatModelHelper.cleanUp(model = model)
        TaskType.LLM_PROMPT_LAB -> LlmChatModelHelper.cleanUp(model = model)
        TaskType.LLM_ASK_IMAGE -> LlmChatModelHelper.cleanUp(model = model)
        TaskType.IMAGE_GENERATION -> ImageGenerationModelHelper.cleanUp(model = model)
        TaskType.TEST_TASK_1 -> {}
        TaskType.TEST_TASK_2 -> {}
      }
      model.instance = null
      model.initializing = false
      updateModelInitializationStatus(
        model = model, status = ModelInitializationStatusType.NOT_INITIALIZED
      )
    } else {
      // When model is being initialized and we are trying to clean it up at same time, we mark it
      // to clean up and it will be cleaned up after initialization is done.
      if (model.initializing) {
        model.cleanUpAfterInit = true
      }
    }
  }

  fun setDownloadStatus(curModel: Model, status: ModelDownloadStatus) {
    // Update model download progress.
    val curModelDownloadStatus = uiState.value.modelDownloadStatus.toMutableMap()
    curModelDownloadStatus[curModel.name] = status
    val newUiState = uiState.value.copy(modelDownloadStatus = curModelDownloadStatus)

    // Delete downloaded file if status is failed or not_downloaded.
    if (status.status == ModelDownloadStatusType.FAILED || status.status == ModelDownloadStatusType.NOT_DOWNLOADED) {
      deleteFileFromExternalFilesDir(curModel.downloadFileName)
    }

    _uiState.update { newUiState }
  }

  fun addTextInputHistory(text: String) {
    if (uiState.value.textInputHistory.indexOf(text) < 0) {
      val newHistory = uiState.value.textInputHistory.toMutableList()
      newHistory.add(0, text)
      if (newHistory.size > TEXT_INPUT_HISTORY_MAX_SIZE) {
        newHistory.removeAt(newHistory.size - 1)
      }
      _uiState.update { _uiState.value.copy(textInputHistory = newHistory) }
      dataStoreRepository.saveTextInputHistory(_uiState.value.textInputHistory)
    } else {
      promoteTextInputHistoryItem(text)
    }
  }

  fun promoteTextInputHistoryItem(text: String) {
    val index = uiState.value.textInputHistory.indexOf(text)
    if (index >= 0) {
      val newHistory = uiState.value.textInputHistory.toMutableList()
      newHistory.removeAt(index)
      newHistory.add(0, text)
      _uiState.update { _uiState.value.copy(textInputHistory = newHistory) }
      dataStoreRepository.saveTextInputHistory(_uiState.value.textInputHistory)
    }
  }

  fun deleteTextInputHistory(text: String) {
    val index = uiState.value.textInputHistory.indexOf(text)
    if (index >= 0) {
      val newHistory = uiState.value.textInputHistory.toMutableList()
      newHistory.removeAt(index)
      _uiState.update { _uiState.value.copy(textInputHistory = newHistory) }
      dataStoreRepository.saveTextInputHistory(_uiState.value.textInputHistory)
    }
  }

  fun clearTextInputHistory() {
    _uiState.update { _uiState.value.copy(textInputHistory = mutableListOf()) }
    dataStoreRepository.saveTextInputHistory(_uiState.value.textInputHistory)
  }

  fun readThemeOverride(): String {
    return dataStoreRepository.readThemeOverride()
  }

  fun saveThemeOverride(theme: String) {
    dataStoreRepository.saveThemeOverride(theme = theme)
  }

  fun getModelUrlResponse(model: Model, accessToken: String? = null): Int {
    try {
      val url = URL(model.url)
      val connection = url.openConnection() as HttpURLConnection
      if (accessToken != null) {
        connection.setRequestProperty(
          "Authorization", "Bearer $accessToken"
        )
      }
      connection.connect()

      // Report the result.
      return connection.responseCode
    } catch (e: Exception) {
      Log.e(TAG, "$e")
      return -1
    }
  }

  fun addImportedLlmModel(info: ImportedModelInfo) {
    Log.d(TAG, "adding imported llm model: $info")

    // Create model.
    val model = createModelFromImportedModelInfo(info = info)

    for (task in listOf(TASK_LLM_ASK_IMAGE, TASK_LLM_PROMPT_LAB, TASK_LLM_CHAT)) {
      // Remove duplicated imported model if existed.
      val modelIndex = task.models.indexOfFirst { info.fileName == it.name && it.imported }
      if (modelIndex >= 0) {
        Log.d(TAG, "duplicated imported model found in task. Removing it first")
        task.models.removeAt(modelIndex)
      }
      if (task == TASK_LLM_ASK_IMAGE && model.llmSupportImage || task != TASK_LLM_ASK_IMAGE) {
        task.models.add(model)
      }
      task.updateTrigger.value = System.currentTimeMillis()
    }

    // Add initial status and states.
    val modelDownloadStatus = uiState.value.modelDownloadStatus.toMutableMap()
    val modelInstances = uiState.value.modelInitializationStatus.toMutableMap()
    modelDownloadStatus[model.name] = ModelDownloadStatus(
      status = ModelDownloadStatusType.SUCCEEDED,
      receivedBytes = info.fileSize,
      totalBytes = info.fileSize
    )
    modelInstances[model.name] =
      ModelInitializationStatus(status = ModelInitializationStatusType.NOT_INITIALIZED)

    // Update ui state.
    _uiState.update {
      uiState.value.copy(
        tasks = uiState.value.tasks.toList(),
        modelDownloadStatus = modelDownloadStatus,
        modelInitializationStatus = modelInstances
      )
    }

    // Add to preference storage.
    val importedModels = dataStoreRepository.readImportedModels().toMutableList()
    val importedModelIndex = importedModels.indexOfFirst { info.fileName == it.fileName }
    if (importedModelIndex >= 0) {
      Log.d(TAG, "duplicated imported model found in preference storage. Removing it first")
      importedModels.removeAt(importedModelIndex)
    }
    importedModels.add(info)
    dataStoreRepository.saveImportedModels(importedModels = importedModels)
  }

  fun getTokenStatusAndData(): TokenStatusAndData {
    // Try to load token data from DataStore.
    var tokenStatus = TokenStatus.NOT_STORED
    Log.d(TAG, "Reading token data from data store...")
    val tokenData = dataStoreRepository.readAccessTokenData()

    // Token exists.
    if (tokenData != null) {
      Log.d(TAG, "Token exists and loaded.")

      // Check expiration (with 5-minute buffer).
      val curTs = System.currentTimeMillis()
      val expirationTs = tokenData.expiresAtMs - 5 * 60
      Log.d(
        TAG,
        "Checking whether token has expired or not. Current ts: $curTs, expires at: $expirationTs"
      )
      if (curTs >= expirationTs) {
        Log.d(TAG, "Token expired!")
        tokenStatus = TokenStatus.EXPIRED
      } else {
        Log.d(TAG, "Token not expired.")
        tokenStatus = TokenStatus.NOT_EXPIRED
        curAccessToken = tokenData.accessToken
      }
    } else {
      Log.d(TAG, "Token doesn't exists.")
    }

    return TokenStatusAndData(status = tokenStatus, data = tokenData)
  }

  fun getAuthorizationRequest(): AuthorizationRequest {
    return AuthorizationRequest.Builder(
      AuthConfig.authServiceConfig,
      AuthConfig.clientId,
      ResponseTypeValues.CODE,
      Uri.parse(AuthConfig.redirectUri)
    ).setScope("read-repos").build()
  }

  fun handleAuthResult(result: ActivityResult, onTokenRequested: (TokenRequestResult) -> Unit) {
    val dataIntent = result.data
    if (dataIntent == null) {
      onTokenRequested(
        TokenRequestResult(
          status = TokenRequestResultType.FAILED, errorMessage = "Empty auth result"
        )
      )
      return
    }

    val response = AuthorizationResponse.fromIntent(dataIntent)
    val exception = AuthorizationException.fromIntent(dataIntent)

    when {
      response?.authorizationCode != null -> {
        // Authorization successful, exchange the code for tokens
        var errorMessage: String? = null
        authService.performTokenRequest(
          response.createTokenExchangeRequest()
        ) { tokenResponse, tokenEx ->
          if (tokenResponse != null) {
            if (tokenResponse.accessToken == null) {
              errorMessage = "Empty access token"
            } else if (tokenResponse.refreshToken == null) {
              errorMessage = "Empty refresh token"
            } else if (tokenResponse.accessTokenExpirationTime == null) {
              errorMessage = "Empty expiration time"
            } else {
              // Token exchange successful. Store the tokens securely
              Log.d(TAG, "Token exchange successful. Storing tokens...")
              saveAccessToken(
                accessToken = tokenResponse.accessToken!!,
                refreshToken = tokenResponse.refreshToken!!,
                expiresAt = tokenResponse.accessTokenExpirationTime!!
              )
              curAccessToken = tokenResponse.accessToken!!
              Log.d(TAG, "Token successfully saved.")
            }
          } else if (tokenEx != null) {
            errorMessage = "Token exchange failed: ${tokenEx.message}"
          } else {
            errorMessage = "Token exchange failed"
          }
          if (errorMessage == null) {
            onTokenRequested(TokenRequestResult(status = TokenRequestResultType.SUCCEEDED))
          } else {
            onTokenRequested(
              TokenRequestResult(
                status = TokenRequestResultType.FAILED, errorMessage = errorMessage
              )
            )
          }
        }
      }

      exception != null -> {
        onTokenRequested(
          TokenRequestResult(
            status = if (exception.message == "User cancelled flow") TokenRequestResultType.USER_CANCELLED else TokenRequestResultType.FAILED,
            errorMessage = "${exception.message}"
          )
        )
      }

      else -> {
        onTokenRequested(
          TokenRequestResult(
            status = TokenRequestResultType.USER_CANCELLED,
          )
        )
      }
    }
  }

  fun saveAccessToken(accessToken: String, refreshToken: String, expiresAt: Long) {
    dataStoreRepository.saveAccessTokenData(
      accessToken = accessToken,
      refreshToken = refreshToken,
      expiresAt = expiresAt,
    )
  }

  fun clearAccessToken() {
    dataStoreRepository.clearAccessTokenData()
  }

  private fun processPendingDownloads() {
    Log.d(TAG, "In-progress worker infos: $inProgressWorkInfos")

    // Iterate through the inProgressWorkInfos and retrieve the corresponding modes.
    // Those models are the ones that have not finished downloading.
    val models: MutableList<Model> = mutableListOf()
    for (info in inProgressWorkInfos) {
      getModelByName(info.modelName)?.let { model ->
        models.add(model)
      }
    }

    // Cancel all pending downloads for the retrieved models.
    downloadRepository.cancelAll(models) {
      Log.d(TAG, "All pending work is cancelled")

      viewModelScope.launch(Dispatchers.IO) {
        // Kick off downloads for these models .
        withContext(Dispatchers.Main) {
          val tokenStatusAndData = getTokenStatusAndData()
          for (info in inProgressWorkInfos) {
            val model: Model? = getModelByName(info.modelName)
            if (model != null) {
              if (tokenStatusAndData.status == TokenStatus.NOT_EXPIRED && tokenStatusAndData.data != null) {
                model.accessToken = tokenStatusAndData.data.accessToken
              }
              Log.d(TAG, "Sending a new download request for '${model.name}'")
              downloadRepository.downloadModel(
                model, onStatusUpdated = this@ModelManagerViewModel::setDownloadStatus
              )
            }
          }
        }
      }
    }
  }

  fun loadModelAllowlist() {
    _uiState.update {
      uiState.value.copy(
        loadingModelAllowlist = true, loadingModelAllowlistError = ""
      )
    }

    viewModelScope.launch(Dispatchers.IO) {
      try {
        // Load model allowlist json.
        Log.d(TAG, "Loading model allowlist from internet...")
        val data = getJsonResponse<ModelAllowlist>(url = MODEL_ALLOWLIST_URL)
        var modelAllowlist: ModelAllowlist? = data?.jsonObj

        if (modelAllowlist == null) {
          Log.d(TAG, "Failed to load model allowlist from internet. Trying to load it from disk")
          modelAllowlist = readModelAllowlistFromDisk()
        } else {
          Log.d(TAG, "Done: loading model allowlist from internet")
          saveModelAllowlistToDisk(modelAllowlistContent = data?.textContent ?: "{}")
        }

        if (modelAllowlist == null) {
          _uiState.update { uiState.value.copy(loadingModelAllowlistError = "Failed to load model list") }
          return@launch
        }

        Log.d(TAG, "Allowlist: $modelAllowlist")

        // Convert models in the allowlist.
        TASK_LLM_CHAT.models.clear()
        TASK_LLM_PROMPT_LAB.models.clear()
        TASK_LLM_ASK_IMAGE.models.clear()
        for (allowedModel in modelAllowlist.models) {
          if (allowedModel.disabled == true) {
            continue
          }

          val model = allowedModel.toModel()
          if (allowedModel.taskTypes.contains(TASK_LLM_CHAT.type.id)) {
            TASK_LLM_CHAT.models.add(model)
          }
          if (allowedModel.taskTypes.contains(TASK_LLM_PROMPT_LAB.type.id)) {
            TASK_LLM_PROMPT_LAB.models.add(model)
          }
          if (allowedModel.taskTypes.contains(TASK_LLM_ASK_IMAGE.type.id)) {
            TASK_LLM_ASK_IMAGE.models.add(model)
          }
        }

        // Pre-process all tasks.
        processTasks()

        // Update UI state.
        val newUiState = createUiState()
        _uiState.update {
          newUiState.copy(
            loadingModelAllowlist = false,
          )
        }

        // Process pending downloads.
        processPendingDownloads()
      } catch (e: Exception) {
        e.printStackTrace()
      }
    }
  }

  private fun saveModelAllowlistToDisk(modelAllowlistContent: String) {
    try {
      Log.d(TAG, "Saving model allowlist to disk...")
      val file = File(externalFilesDir, MODEL_ALLOWLIST_FILENAME)
      file.writeText(modelAllowlistContent)
      Log.d(TAG, "Done: saving model allowlist to disk.")
    } catch (e: Exception) {
      Log.e(TAG, "failed to write model allowlist to disk", e)
    }
  }

  private fun readModelAllowlistFromDisk(): ModelAllowlist? {
    try {
      Log.d(TAG, "Reading model allowlist from disk...")
      val file = File(externalFilesDir, MODEL_ALLOWLIST_FILENAME)
      if (file.exists()) {
        val content = file.readText()
        Log.d(TAG, "Model allowlist content from local file: $content")
        val json = Json {
          // Handle potential extra fields
          ignoreUnknownKeys = true
          allowComments = true
          allowTrailingComma = true
        }
        return json.decodeFromString<ModelAllowlist>(content)
      }
    } catch (e: Exception) {
      Log.e(TAG, "failed to read model allowlist from disk", e)
      return null
    }

    return null
  }

  private fun isModelPartiallyDownloaded(model: Model): Boolean {
    return inProgressWorkInfos.find { it.modelName == model.name } != null
  }

  private fun createEmptyUiState(): ModelManagerUiState {
    return ModelManagerUiState(
      tasks = listOf(),
      modelDownloadStatus = mapOf(),
      modelInitializationStatus = mapOf(),
    )
  }

  private fun createUiState(): ModelManagerUiState {
    val modelDownloadStatus: MutableMap<String, ModelDownloadStatus> = mutableMapOf()
    val modelInstances: MutableMap<String, ModelInitializationStatus> = mutableMapOf()
    for (task in TASKS) {
      for (model in task.models) {
        modelDownloadStatus[model.name] = getModelDownloadStatus(model = model)
        modelInstances[model.name] =
          ModelInitializationStatus(status = ModelInitializationStatusType.NOT_INITIALIZED)
      }
    }

    // Load imported models.
    for (importedModel in dataStoreRepository.readImportedModels()) {
      Log.d(TAG, "stored imported model: $importedModel")

      // Create model.
      val model = createModelFromImportedModelInfo(info = importedModel)

      // Add to task.
      TASK_LLM_CHAT.models.add(model)
      TASK_LLM_PROMPT_LAB.models.add(model)
      if (model.llmSupportImage) {
        TASK_LLM_ASK_IMAGE.models.add(model)
      }

      // Update status.
      modelDownloadStatus[model.name] = ModelDownloadStatus(
        status = ModelDownloadStatusType.SUCCEEDED,
        receivedBytes = importedModel.fileSize,
        totalBytes = importedModel.fileSize
      )
    }

    val textInputHistory = dataStoreRepository.readTextInputHistory()
    Log.d(TAG, "text input history: $textInputHistory")

    Log.d(TAG, "model download status: $modelDownloadStatus")
    return ModelManagerUiState(
      tasks = TASKS.toList(),
      modelDownloadStatus = modelDownloadStatus,
      modelInitializationStatus = modelInstances,
      textInputHistory = textInputHistory,
    )
  }

  private fun createModelFromImportedModelInfo(info: ImportedModelInfo): Model {
    val accelerators: List<Accelerator> = (convertValueToTargetType(
      info.defaultValues[ConfigKey.COMPATIBLE_ACCELERATORS.label]!!, ValueType.STRING
    ) as String).split(",").mapNotNull { acceleratorLabel ->
      when (acceleratorLabel.trim()) {
        Accelerator.GPU.label -> Accelerator.GPU
        Accelerator.CPU.label -> Accelerator.CPU
        else -> null // Ignore unknown accelerator labels
      }
    }
    val configs: List<Config> = createLlmChatConfigs(
      defaultMaxToken = convertValueToTargetType(
        info.defaultValues[ConfigKey.DEFAULT_MAX_TOKENS.label]!!, ValueType.INT
      ) as Int,
      defaultTopK = convertValueToTargetType(
        info.defaultValues[ConfigKey.DEFAULT_TOPK.label]!!, ValueType.INT
      ) as Int,
      defaultTopP = convertValueToTargetType(
        info.defaultValues[ConfigKey.DEFAULT_TOPP.label]!!, ValueType.FLOAT
      ) as Float,
      defaultTemperature = convertValueToTargetType(
        info.defaultValues[ConfigKey.DEFAULT_TEMPERATURE.label]!!, ValueType.FLOAT
      ) as Float,
      accelerators = accelerators,
    )
    val llmSupportImage = convertValueToTargetType(
      info.defaultValues[ConfigKey.SUPPORT_IMAGE.label] ?: false, ValueType.BOOLEAN
    ) as Boolean
    val model = Model(
      name = info.fileName,
      url = "",
      configs = configs,
      sizeInBytes = info.fileSize,
      downloadFileName = "$IMPORTS_DIR/${info.fileName}",
      showBenchmarkButton = false,
      showRunAgainButton = false,
      imported = true,
      llmSupportImage = llmSupportImage,
    )
    model.preProcess()

    return model
  }

  /**
   * Retrieves the download status of a model.
   *
   * This function determines the download status of a given model by checking if it's fully
   * downloaded, partially downloaded, or not downloaded at all. It also retrieves the received and
   * total bytes for partially downloaded models.
   */
  private fun getModelDownloadStatus(model: Model): ModelDownloadStatus {
    var status = ModelDownloadStatusType.NOT_DOWNLOADED
    var receivedBytes = 0L
    var totalBytes = 0L
    if (isModelDownloaded(model = model)) {
      if (isModelPartiallyDownloaded(model = model)) {
        status = ModelDownloadStatusType.PARTIALLY_DOWNLOADED
        val file = File(externalFilesDir, model.downloadFileName)
        receivedBytes = file.length()
        totalBytes = model.totalBytes
      } else {
        status = ModelDownloadStatusType.SUCCEEDED
      }
    }
    return ModelDownloadStatus(
      status = status, receivedBytes = receivedBytes, totalBytes = totalBytes
    )
  }

  private fun isFileInExternalFilesDir(fileName: String): Boolean {
    if (externalFilesDir != null) {
      val file = File(externalFilesDir, fileName)
      return file.exists()
    } else {
      return false
    }
  }

  private fun deleteFileFromExternalFilesDir(fileName: String) {
    if (isFileInExternalFilesDir(fileName)) {
      val file = File(externalFilesDir, fileName)
      file.delete()
    }
  }

  private fun deleteDirFromExternalFilesDir(dir: String) {
    if (isFileInExternalFilesDir(dir)) {
      val file = File(externalFilesDir, dir)
      file.deleteRecursively()
    }
  }

  private fun updateModelInitializationStatus(
    model: Model, status: ModelInitializationStatusType, error: String = ""
  ) {
    val curModelInstance = uiState.value.modelInitializationStatus.toMutableMap()
    curModelInstance[model.name] = ModelInitializationStatus(status = status, error = error)
    val newUiState = uiState.value.copy(modelInitializationStatus = curModelInstance)
    _uiState.update { newUiState }
  }

  private fun isModelDownloaded(model: Model): Boolean {
    val downloadedFileExists = model.downloadFileName.isNotEmpty() && isFileInExternalFilesDir(
      listOf(
        model.normalizedName, model.version, model.downloadFileName
      ).joinToString(File.separator)
    )

    val unzippedDirectoryExists =
      model.isZip && model.unzipDir.isNotEmpty() && isFileInExternalFilesDir(
        listOf(
          model.normalizedName, model.version, model.unzipDir
        ).joinToString(File.separator)
      )

    // Will also return true if model is partially downloaded.
    return downloadedFileExists || unzippedDirectoryExists
  }
}
