/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.borealbit.ai.edgemind.ui.modelmanager

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.borealbit.ai.edgemind.R

/**
 * EdgeMind Model Manager based on Figma design
 * Features a clean interface for managing AI models
 */
@Composable
fun EdgeMindModelManager(
    models: List<ModelInfo> = emptyList(),
    onBackClick: () -> Unit = {},
    onModelClick: (ModelInfo) -> Unit = {},
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color(0xFF1F1F1F)) // Dark background from design
    ) {
        // Header
        ModelsHeader()
        
        // Installed section
        InstalledSection()
        
        // Models list
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(models) { model ->
                ModelCard(
                    model = model,
                    onSelect = { onModelClick(model) }
                )
            }
        }
    }
}

@Composable
fun ModelsHeader() {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Icon(
            painter = painterResource(R.drawable.logo_icon),
            contentDescription = "EdgeMind Logo",
            tint = Color.White,
            modifier = Modifier.size(20.dp)
        )
        Text(
            text = "Models",
            color = Color.White,
            fontSize = 18.sp,
            fontWeight = FontWeight.SemiBold
        )
    }
}

@Composable
fun InstalledSection() {
    Text(
        text = "Installed",
        color = Color.White,
        fontSize = 16.sp,
        fontWeight = FontWeight.Medium,
        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
    )
}

@Composable
fun ModelCard(
    model: ModelInfo,
    onSelect: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp)),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF3A3A3A) // Card background from design
        ),
        onClick = onSelect
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Model icon
            Icon(
                painter = painterResource(R.drawable.logo_icon),
                contentDescription = "Model Icon",
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
            
            // Model info
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = model.name,
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = model.parameters,
                    color = Color(0xFF61758A),
                    fontSize = 12.sp
                )
            }
            
            // Status indicator
            if (model.isInstalled) {
                Icon(
                    painter = painterResource(R.drawable.logo_icon),
                    contentDescription = "Installed",
                    tint = Color(0xFF00C851),
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

// Data class for model information
data class ModelInfo(
    val name: String,
    val parameters: String,
    val isInstalled: Boolean = false,
    val downloadProgress: Float = 0f
)

// Sample data matching Figma design
val sampleModels = listOf(
    ModelInfo(
        name = "EdgeMind Chat",
        parameters = "1.2B parameters",
        isInstalled = true
    ),
    ModelInfo(
        name = "EdgeMind Vision",
        parameters = "2.7B parameters",
        isInstalled = false
    ),
    ModelInfo(
        name = "EdgeMind Code",
        parameters = "6.7B parameters",
        isInstalled = false
    )
)