/*
 * Copyright 2025 BorealBit
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.borealbit.gbdietitian.android.presentation.navigation

import android.util.Log
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.navDeepLink
import com.borealbit.gbdietitian.android.presentation.navigation.NavigationAnimations.slideInFromRight
import com.borealbit.gbdietitian.android.presentation.navigation.NavigationAnimations.slideOutToLeft

/**
 * 营养师应用主导航图
 * 
 * 学习自 EdgeMind 的现代导航架构
 * 集成了类型安全路由、动画过渡和深度链接
 */

private const val TAG = "NutritionNavGraph"

@Composable
fun NutritionNavHost(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    startDestination: String = HomeDestination.route
) {
    NavHost(
        navController = navController,
        startDestination = startDestination,
        modifier = modifier,
        enterTransition = { EnterTransition.None },
        exitTransition = { ExitTransition.None }
    ) {
        
        // 主页
        composable(
            route = HomeDestination.route,
            enterTransition = { SceneAnimations.bottomNavEnter() },
            exitTransition = { SceneAnimations.bottomNavExit() }
        ) {
            // HomeScreen(
            //     navigateToFoodDetail = { foodId ->
            //         navController.navigate(NavigationUtils.buildFoodDetailRoute(foodId))
            //     },
            //     navigateToScan = {
            //         navController.navigate(ScanDestination.route)
            //     },
            //     navigateToAiChat = {
            //         navController.navigate(NavigationUtils.buildAiChatRoute())
            //     }
            // )
        }
        
        // 扫描页面
        composable(
            route = ScanDestination.route,
            enterTransition = { SceneAnimations.scanEnter() },
            exitTransition = { SceneAnimations.scanExit() }
        ) {
            // ScanScreen(
            //     navigateUp = { navController.navigateUp() },
            //     navigateToFoodDetail = { foodId ->
            //         navController.navigate(NavigationUtils.buildFoodDetailRoute(foodId))
            //     }
            // )
        }
        
        // 分析页面
        composable(
            route = AnalysisDestination.route,
            enterTransition = { SceneAnimations.analysisEnter() },
            exitTransition = { SceneAnimations.analysisExit() }
        ) {
            // AnalysisScreen(
            //     navigateToNutritionLog = { date ->
            //         navController.navigate(NavigationUtils.buildNutritionLogRoute(date))
            //     },
            //     navigateToReports = {
            //         navController.navigate(ReportsDestination.route)
            //     }
            // )
        }
        
        // 个人资料页面
        composable(
            route = ProfileDestination.route,
            enterTransition = { SceneAnimations.bottomNavEnter() },
            exitTransition = { SceneAnimations.bottomNavExit() }
        ) {
            // ProfileScreen(
            //     navigateToSettings = {
            //         navController.navigate(SettingsDestination.route)
            //     },
            //     navigateToGoals = {
            //         navController.navigate(GoalsDestination.route)
            //     },
            //     navigateToHistory = {
            //         navController.navigate(HistoryDestination.route)
            //     }
            // )
        }
        
        // 食物详情页面
        composable(
            route = FoodDetailDestination.routeWithArgs,
            arguments = listOf(
                navArgument(NavigationArgs.FOOD_ID) { type = NavType.StringType }
            ),
            deepLinks = listOf(
                navDeepLink { uriPattern = DeepLinks.FOOD_DETAIL }
            ),
            enterTransition = { AnimationSelector.getEnterAnimation(this, FoodDetailDestination.route) },
            exitTransition = { AnimationSelector.getExitAnimation(this, FoodDetailDestination.route) }
        ) { backStackEntry ->
            val foodId = backStackEntry.arguments?.getString(NavigationArgs.FOOD_ID) ?: ""
            // FoodDetailScreen(
            //     foodId = foodId,
            //     navigateUp = { navController.navigateUp() },
            //     navigateToNutritionAnalysis = { foodId ->
            //         navController.navigate(NavigationUtils.buildNutritionAnalysisRoute(foodId))
            //     }
            // )
        }
        
        // 营养日志页面
        composable(
            route = NutritionLogDestination.routeWithArgs,
            arguments = listOf(
                navArgument(NavigationArgs.DATE) { 
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                }
            ),
            deepLinks = listOf(
                navDeepLink { uriPattern = DeepLinks.NUTRITION_LOG }
            ),
            enterTransition = { slideInFromRight() },
            exitTransition = { slideOutToLeft() }
        ) { backStackEntry ->
            val date = backStackEntry.arguments?.getString(NavigationArgs.DATE)
            // NutritionLogScreen(
            //     date = date,
            //     navigateUp = { navController.navigateUp() },
            //     navigateToFoodDetail = { foodId ->
            //         navController.navigate(NavigationUtils.buildFoodDetailRoute(foodId))
            //     }
            // )
        }
        
        // 食物搜索页面
        composable(
            route = FoodSearchDestination.routeWithArgs,
            arguments = listOf(
                navArgument(NavigationArgs.QUERY) { 
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                }
            ),
            enterTransition = { slideInFromRight() },
            exitTransition = { slideOutToLeft() }
        ) { backStackEntry ->
            val query = backStackEntry.arguments?.getString(NavigationArgs.QUERY)
            // FoodSearchScreen(
            //     initialQuery = query,
            //     navigateUp = { navController.navigateUp() },
            //     navigateToFoodDetail = { foodId ->
            //         navController.navigate(NavigationUtils.buildFoodDetailRoute(foodId))
            //     }
            // )
        }
        
        // 相机页面
        composable(
            route = CameraDestination.route,
            enterTransition = { SceneAnimations.scanEnter() },
            exitTransition = { SceneAnimations.scanExit() }
        ) {
            // CameraScreen(
            //     navigateUp = { navController.navigateUp() },
            //     navigateToFoodDetail = { foodId ->
            //         navController.navigate(NavigationUtils.buildFoodDetailRoute(foodId))
            //     }
            // )
        }
        
        // 设置页面
        composable(
            route = SettingsDestination.route,
            enterTransition = { SceneAnimations.modalEnter() },
            exitTransition = { SceneAnimations.modalExit() }
        ) {
            // SettingsScreen(
            //     navigateUp = { navController.navigateUp() },
            //     navigateToHealthConnect = {
            //         navController.navigate(HealthConnectDestination.route)
            //     }
            // )
        }
        
        // 目标设置页面
        composable(
            route = GoalsDestination.route,
            enterTransition = { SceneAnimations.modalEnter() },
            exitTransition = { SceneAnimations.modalExit() }
        ) {
            // GoalsScreen(
            //     navigateUp = { navController.navigateUp() }
            // )
        }
        
        // 历史记录页面
        composable(
            route = HistoryDestination.route,
            enterTransition = { slideInFromRight() },
            exitTransition = { slideOutToLeft() }
        ) {
            // HistoryScreen(
            //     navigateUp = { navController.navigateUp() },
            //     navigateToNutritionLog = { date ->
            //         navController.navigate(NavigationUtils.buildNutritionLogRoute(date))
            //     }
            // )
        }
        
        // 报告页面
        composable(
            route = ReportsDestination.route,
            enterTransition = { SceneAnimations.analysisEnter() },
            exitTransition = { SceneAnimations.analysisExit() }
        ) {
            // ReportsScreen(
            //     navigateUp = { navController.navigateUp() }
            // )
        }
        
        // AI 聊天页面
        composable(
            route = AiChatDestination.routeWithArgs,
            arguments = listOf(
                navArgument(NavigationArgs.SESSION_ID) { 
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                }
            ),
            deepLinks = listOf(
                navDeepLink { uriPattern = DeepLinks.AI_CHAT }
            ),
            enterTransition = { AnimationSelector.getEnterAnimation(this, AiChatDestination.route) },
            exitTransition = { AnimationSelector.getExitAnimation(this, AiChatDestination.route) }
        ) { backStackEntry ->
            val sessionId = backStackEntry.arguments?.getString(NavigationArgs.SESSION_ID)
            // AiChatScreen(
            //     sessionId = sessionId,
            //     navigateUp = { navController.navigateUp() },
            //     navigateToFoodDetail = { foodId ->
            //         navController.navigate(NavigationUtils.buildFoodDetailRoute(foodId))
            //     }
            // )
        }
        
        // 营养分析页面
        composable(
            route = NutritionAnalysisDestination.routeWithArgs,
            arguments = listOf(
                navArgument(NavigationArgs.FOOD_ID) { type = NavType.StringType }
            ),
            enterTransition = { AnimationSelector.getEnterAnimation(this, NutritionAnalysisDestination.route) },
            exitTransition = { AnimationSelector.getExitAnimation(this, NutritionAnalysisDestination.route) }
        ) { backStackEntry ->
            val foodId = backStackEntry.arguments?.getString(NavigationArgs.FOOD_ID) ?: ""
            // NutritionAnalysisScreen(
            //     foodId = foodId,
            //     navigateUp = { navController.navigateUp() }
            // )
        }
        
        // Health Connect 页面
        composable(
            route = HealthConnectDestination.route,
            enterTransition = { slideInFromRight() },
            exitTransition = { slideOutToLeft() }
        ) {
            // HealthConnectScreen(
            //     navigateUp = { navController.navigateUp() }
            // )
        }
    }
    
    // 处理深度链接
    HandleDeepLinks(navController)
}

/**
 * 深度链接处理
 * 学习自 EdgeMind 的深度链接实现
 */
@Composable
private fun HandleDeepLinks(navController: NavHostController) {
    val intent = androidx.activity.compose.LocalActivity.current?.intent
    val data = intent?.data
    
    if (data != null) {
        intent.data = null
        Log.d(TAG, "Deep link clicked: $data")
        
        val route = NavigationUtils.parseDeepLink(data.toString())
        if (route != null) {
            navController.navigate(route)
        }
    }
}
