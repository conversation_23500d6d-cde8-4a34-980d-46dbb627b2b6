package com.borealbit.gbdietitian.android.presentation.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.borealbit.gbdietitian.android.presentation.theme.GuoBiaoDietitianTheme
import com.borealbit.gbdietitian.android.presentation.theme.NutrientDanger
import com.borealbit.gbdietitian.android.presentation.theme.NutrientGood
import com.borealbit.gbdietitian.android.presentation.theme.NutrientWarning
import com.borealbit.gbdietitian.android.presentation.theme.nutritionColors
import com.borealbit.gbdietitian.android.presentation.theme.getNutrientColor
import com.borealbit.gbdietitian.android.presentation.theme.getNutritionGradeColor

@Composable
fun NutritionCard(
    title: String,
    current: Double,
    target: Double,
    unit: String,
    icon: ImageVector? = null,
    modifier: Modifier = Modifier
) {
    val progress = (current / target).toFloat()
    val status = when {
        progress <= 0.7f -> NutritionStatus.GOOD
        progress <= 1.0f -> NutritionStatus.WARNING
        else -> NutritionStatus.DANGER
    }
    val statusColor = getNutritionStatusColor(status)
    val nutrientColor = getNutrientColor(title)

    Card(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                icon?.let {
                    Icon(
                        imageVector = it,
                        contentDescription = null,
                        tint = statusColor,
                        modifier = Modifier.size(20.dp)
                    )
                }
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Bottom
            ) {
                Column {
                    Text(
                        text = "${current.toInt()}",
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold,
                        color = nutrientColor
                    )
                    Text(
                        text = "/ ${target.toInt()} $unit",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                Text(
                    text = "${(progress * 100).toInt()}%",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    color = statusColor
                )
            }

            LinearProgressIndicator(
                progress = progress.coerceIn(0f, 1f),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(6.dp),
                color = nutrientColor,
                trackColor = MaterialTheme.colorScheme.surfaceVariant
            )

            if (progress > 1f) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Warning,
                        contentDescription = null,
                        tint = MaterialTheme.nutritionColors.badColor,
                        modifier = Modifier.size(16.dp)
                    )
                    Text(
                        text = "超标 ${((progress - 1) * 100).toInt()}%",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.nutritionColors.badColor
                    )
                }
            }
        }
    }
}

@Composable
fun getNutritionStatusColor(status: NutritionStatus): Color {
    return when (status) {
        NutritionStatus.GOOD -> MaterialTheme.nutritionColors.excellentColor
        NutritionStatus.WARNING -> MaterialTheme.nutritionColors.fairColor
        NutritionStatus.DANGER -> MaterialTheme.nutritionColors.badColor
    }
}

enum class NutritionStatus {
    GOOD,
    WARNING,
    DANGER
}

@Preview(showBackground = true)
@Composable
fun NutritionCardPreview() {
    GuoBiaoDietitianTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            NutritionCard(
                title = "卡路里",
                current = 1650.0,
                target = 2000.0,
                unit = "kcal"
            )
            NutritionCard(
                title = "蛋白质",
                current = 85.0,
                target = 70.0,
                unit = "g",
                icon = Icons.Default.Warning
            )
        }
    }
}
