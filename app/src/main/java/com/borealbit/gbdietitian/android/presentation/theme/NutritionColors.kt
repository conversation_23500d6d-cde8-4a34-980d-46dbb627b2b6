/*
 * Copyright 2025 BorealBit
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.borealbit.gbdietitian.android.presentation.theme

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color

/**
 * 营养相关的自定义颜色系统
 * 
 * 学习自 EdgeMind 的 CustomColors 模式
 * 为营养师应用提供专门的颜色主题
 */
@Immutable
data class NutritionColors(
    // 营养素类别颜色
    val proteinColor: Color = Color.Transparent,
    val carbsColor: Color = Color.Transparent,
    val fatColor: Color = Color.Transparent,
    val fiberColor: Color = Color.Transparent,
    val sugarColor: Color = Color.Transparent,
    val sodiumColor: Color = Color.Transparent,
    
    // 营养等级颜色
    val excellentColor: Color = Color.Transparent,
    val goodColor: Color = Color.Transparent,
    val fairColor: Color = Color.Transparent,
    val poorColor: Color = Color.Transparent,
    val badColor: Color = Color.Transparent,
    
    // 食物类别颜色
    val vegetableColor: Color = Color.Transparent,
    val fruitColor: Color = Color.Transparent,
    val grainColor: Color = Color.Transparent,
    val proteinFoodColor: Color = Color.Transparent,
    val dairyColor: Color = Color.Transparent,
    val snackColor: Color = Color.Transparent,
    
    // 渐变色
    val nutritionGradient: List<Color> = listOf(),
    val backgroundGradient: List<Color> = listOf(),
    
    // 功能性颜色
    val calorieRingColor: Color = Color.Transparent,
    val goalAchievedColor: Color = Color.Transparent,
    val goalExceededColor: Color = Color.Transparent,
    val scannerOverlayColor: Color = Color.Transparent,
    val aiResponseBubbleColor: Color = Color.Transparent,
    val userInputBubbleColor: Color = Color.Transparent,
)

/**
 * 本地组合提供者
 * 学习自 EdgeMind 的 LocalCustomColors 模式
 */
val LocalNutritionColors = staticCompositionLocalOf { NutritionColors() }

/**
 * 浅色主题的营养颜色
 */
val lightNutritionColors = NutritionColors(
    // 营养素类别颜色
    proteinColor = Color(0xFFE91E63),        // 蛋白质 - 粉红色
    carbsColor = Color(0xFFFF9800),          // 碳水化合物 - 橙色
    fatColor = Color(0xFF9C27B0),            // 脂肪 - 紫色
    fiberColor = Color(0xFF4CAF50),          // 纤维 - 绿色
    sugarColor = Color(0xFFF44336),          // 糖分 - 红色
    sodiumColor = Color(0xFFFF5722),         // 钠 - 深橙色
    
    // 营养等级颜色
    excellentColor = Color(0xFF4CAF50),      // 优秀 - 绿色
    goodColor = Color(0xFF8BC34A),           // 良好 - 浅绿色
    fairColor = Color(0xFFFFC107),           // 一般 - 黄色
    poorColor = Color(0xFFFF9800),           // 较差 - 橙色
    badColor = Color(0xFFF44336),            // 差 - 红色
    
    // 食物类别颜色
    vegetableColor = Color(0xFF4CAF50),      // 蔬菜 - 绿色
    fruitColor = Color(0xFFFF9800),          // 水果 - 橙色
    grainColor = Color(0xFF8D6E63),          // 谷物 - 棕色
    proteinFoodColor = Color(0xFFE91E63),    // 蛋白质食物 - 粉红色
    dairyColor = Color(0xFF2196F3),          // 乳制品 - 蓝色
    snackColor = Color(0xFF9C27B0),          // 零食 - 紫色
    
    // 渐变色
    nutritionGradient = listOf(
        Color(0xFFE8F5E8),
        Color(0xFFC8E6C9)
    ),
    backgroundGradient = listOf(
        Color(0x00F8F9FF),
        Color(0xFFE8F5E8)
    ),
    
    // 功能性颜色
    calorieRingColor = Color(0xFF4CAF50),
    goalAchievedColor = Color(0xFF4CAF50),
    goalExceededColor = Color(0xFFFF9800),
    scannerOverlayColor = Color(0x80000000),
    aiResponseBubbleColor = Color(0xFFE8F5E8),
    userInputBubbleColor = Color(0xFF4CAF50),
)

/**
 * 深色主题的营养颜色
 */
val darkNutritionColors = NutritionColors(
    // 营养素类别颜色 (深色主题下稍微调亮)
    proteinColor = Color(0xFFF48FB1),        // 蛋白质 - 浅粉红色
    carbsColor = Color(0xFFFFB74D),          // 碳水化合物 - 浅橙色
    fatColor = Color(0xFFBA68C8),            // 脂肪 - 浅紫色
    fiberColor = Color(0xFF81C784),          // 纤维 - 浅绿色
    sugarColor = Color(0xFFEF5350),          // 糖分 - 浅红色
    sodiumColor = Color(0xFFFF8A65),         // 钠 - 浅深橙色
    
    // 营养等级颜色
    excellentColor = Color(0xFF81C784),      // 优秀 - 浅绿色
    goodColor = Color(0xFFAED581),           // 良好 - 更浅绿色
    fairColor = Color(0xFFFFD54F),           // 一般 - 浅黄色
    poorColor = Color(0xFFFFB74D),           // 较差 - 浅橙色
    badColor = Color(0xFFEF5350),            // 差 - 浅红色
    
    // 食物类别颜色
    vegetableColor = Color(0xFF81C784),      // 蔬菜 - 浅绿色
    fruitColor = Color(0xFFFFB74D),          // 水果 - 浅橙色
    grainColor = Color(0xFFA1887F),          // 谷物 - 浅棕色
    proteinFoodColor = Color(0xFFF48FB1),    // 蛋白质食物 - 浅粉红色
    dairyColor = Color(0xFF64B5F6),          // 乳制品 - 浅蓝色
    snackColor = Color(0xFFBA68C8),          // 零食 - 浅紫色
    
    // 渐变色
    nutritionGradient = listOf(
        Color(0xFF2E2E2E),
        Color(0xFF1E3A1E)
    ),
    backgroundGradient = listOf(
        Color(0x00121212),
        Color(0xFF1E3A1E)
    ),
    
    // 功能性颜色
    calorieRingColor = Color(0xFF81C784),
    goalAchievedColor = Color(0xFF81C784),
    goalExceededColor = Color(0xFFFFB74D),
    scannerOverlayColor = Color(0x80000000),
    aiResponseBubbleColor = Color(0xFF1E3A1E),
    userInputBubbleColor = Color(0xFF2E5A2E),
)

/**
 * MaterialTheme 扩展属性
 * 学习自 EdgeMind 的扩展模式
 */
val MaterialTheme.nutritionColors: NutritionColors
    @Composable
    @ReadOnlyComposable
    get() = LocalNutritionColors.current

/**
 * 营养素颜色获取函数
 * 根据营养素类型返回对应颜色
 */
@Composable
fun getNutrientColor(nutrientType: String): Color {
    return when (nutrientType.lowercase()) {
        "protein", "蛋白质" -> MaterialTheme.nutritionColors.proteinColor
        "carbs", "carbohydrate", "碳水化合物" -> MaterialTheme.nutritionColors.carbsColor
        "fat", "脂肪" -> MaterialTheme.nutritionColors.fatColor
        "fiber", "纤维" -> MaterialTheme.nutritionColors.fiberColor
        "sugar", "糖分" -> MaterialTheme.nutritionColors.sugarColor
        "sodium", "钠" -> MaterialTheme.nutritionColors.sodiumColor
        else -> MaterialTheme.colorScheme.primary
    }
}

/**
 * 营养等级颜色获取函数
 * 根据营养等级返回对应颜色
 */
@Composable
fun getNutritionGradeColor(grade: String): Color {
    return when (grade.lowercase()) {
        "excellent", "优秀" -> MaterialTheme.nutritionColors.excellentColor
        "good", "良好" -> MaterialTheme.nutritionColors.goodColor
        "fair", "一般" -> MaterialTheme.nutritionColors.fairColor
        "poor", "较差" -> MaterialTheme.nutritionColors.poorColor
        "bad", "差" -> MaterialTheme.nutritionColors.badColor
        else -> MaterialTheme.colorScheme.primary
    }
}

/**
 * 食物类别颜色获取函数
 * 根据食物类别返回对应颜色
 */
@Composable
fun getFoodCategoryColor(category: String): Color {
    return when (category.lowercase()) {
        "vegetable", "蔬菜" -> MaterialTheme.nutritionColors.vegetableColor
        "fruit", "水果" -> MaterialTheme.nutritionColors.fruitColor
        "grain", "谷物" -> MaterialTheme.nutritionColors.grainColor
        "protein", "蛋白质食物" -> MaterialTheme.nutritionColors.proteinFoodColor
        "dairy", "乳制品" -> MaterialTheme.nutritionColors.dairyColor
        "snack", "零食" -> MaterialTheme.nutritionColors.snackColor
        else -> MaterialTheme.colorScheme.primary
    }
}
