/*
 * Copyright 2025 BorealBit
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.borealbit.gbdietitian.android.data.repository

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.runBlocking

/**
 * DataStore 仓库接口
 * 
 * 学习自 EdgeMind 的简化数据存储模式
 * 用于管理应用的偏好设置和配置
 */
interface DataStoreRepository {
    // 主题相关
    fun getThemeMode(): Flow<String>
    suspend fun setThemeMode(mode: String)
    fun readThemeMode(): String
    
    // 用户偏好
    fun getFirstLaunch(): Flow<Boolean>
    suspend fun setFirstLaunch(isFirst: Boolean)
    
    // 营养目标设置
    fun getDailyCalorieGoal(): Flow<Int>
    suspend fun setDailyCalorieGoal(calories: Int)
    
    // 其他应用设置...
}

/**
 * DataStore 仓库默认实现
 * 
 * 提供简单直接的数据存储操作
 * 学习自 EdgeMind 的实现模式
 */
class DefaultDataStoreRepository(
    private val dataStore: DataStore<Preferences>
) : DataStoreRepository {
    
    companion object {
        private val THEME_MODE_KEY = stringPreferencesKey("theme_mode")
        private val FIRST_LAUNCH_KEY = booleanPreferencesKey("first_launch")
        private val DAILY_CALORIE_GOAL_KEY = stringPreferencesKey("daily_calorie_goal")
        
        // 主题模式常量
        const val THEME_MODE_SYSTEM = "system"
        const val THEME_MODE_LIGHT = "light"
        const val THEME_MODE_DARK = "dark"
        
        // 默认值
        const val DEFAULT_DAILY_CALORIES = 2000
    }
    
    // 主题模式
    override fun getThemeMode(): Flow<String> = dataStore.data.map { preferences ->
        preferences[THEME_MODE_KEY] ?: THEME_MODE_SYSTEM
    }
    
    override suspend fun setThemeMode(mode: String) {
        dataStore.edit { preferences ->
            preferences[THEME_MODE_KEY] = mode
        }
    }
    
    override fun readThemeMode(): String = runBlocking {
        dataStore.data.first()[THEME_MODE_KEY] ?: THEME_MODE_SYSTEM
    }
    
    // 首次启动
    override fun getFirstLaunch(): Flow<Boolean> = dataStore.data.map { preferences ->
        preferences[FIRST_LAUNCH_KEY] ?: true
    }
    
    override suspend fun setFirstLaunch(isFirst: Boolean) {
        dataStore.edit { preferences ->
            preferences[FIRST_LAUNCH_KEY] = isFirst
        }
    }
    
    // 每日卡路里目标
    override fun getDailyCalorieGoal(): Flow<Int> = dataStore.data.map { preferences ->
        preferences[DAILY_CALORIE_GOAL_KEY]?.toIntOrNull() ?: DEFAULT_DAILY_CALORIES
    }
    
    override suspend fun setDailyCalorieGoal(calories: Int) {
        dataStore.edit { preferences ->
            preferences[DAILY_CALORIE_GOAL_KEY] = calories.toString()
        }
    }
}

/**
 * 使用示例：
 * 
 * // 在 Application 中
 * val container = DefaultAppContainer(this, dataStore)
 * 
 * // 在 UI 中
 * val themeMode by container.dataStoreRepository.getThemeMode().collectAsState()
 * 
 * // 在 ViewModel 中（通过 Hilt 注入）
 * @HiltViewModel
 * class SettingsViewModel @Inject constructor(
 *     private val application: Application
 * ) : ViewModel() {
 *     private val container = (application as GuoBiaoDietitianApplication).container
 *     
 *     fun updateTheme(mode: String) {
 *         viewModelScope.launch {
 *             container.dataStoreRepository.setThemeMode(mode)
 *         }
 *     }
 * }
 */
