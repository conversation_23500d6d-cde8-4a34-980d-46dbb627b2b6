package com.borealbit.gbdietitian.android

import android.app.Application
import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import com.borealbit.gbdietitian.android.core.AppContainer
import com.borealbit.gbdietitian.android.core.DefaultAppContainer
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

// 学习自 EdgeMind 的 DataStore 配置
private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "nutrition_preferences")

@HiltAndroidApp
class GuoBiaoDietitianApplication : Application(), Configuration.Provider {

    @Inject
    lateinit var workerFactory: HiltWorkerFactory

    /**
     * AppContainer 实例，学习自 EdgeMind 的依赖注入模式
     * 用于应用级别的依赖管理，与 Hilt 形成互补
     */
    lateinit var container: AppContainer

    override fun getWorkManagerConfiguration(): Configuration {
        return Configuration.Builder()
            .setWorkerFactory(workerFactory)
            .build()
    }

    override fun onCreate() {
        super.onCreate()

        // 初始化 AppContainer（学习自 EdgeMind）
        container = DefaultAppContainer(this, dataStore)

        // 记录应用启动信息
        writeLaunchInfo(context = this)
    }
}

/**
 * 写入启动信息
 * 学习自 EdgeMind 的启动日志记录
 */
private fun writeLaunchInfo(context: Context) {
    android.util.Log.i(
        "GuoBiaoDietitianApp",
        "Application started - Version: ${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})"
    )
}
