/*
 * Copyright 2025 BorealBit
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.borealbit.gbdietitian.android.core

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import com.borealbit.gbdietitian.android.data.repository.DataStoreRepository
import com.borealbit.gbdietitian.android.data.repository.DefaultDataStoreRepository

/**
 * 应用容器接口 - 学习自 EdgeMind 的简化依赖注入模式
 * 
 * 这个接口定义了应用级别的核心依赖，作为 Hilt 的补充
 * 主要用于管理一些全局状态和配置
 */
interface AppContainer {
    val context: Context
    val dataStoreRepository: DataStoreRepository
    // 可以添加其他全局依赖，如：
    // val configManager: ConfigManager
    // val themeManager: ThemeManager
}

/**
 * 默认应用容器实现
 * 
 * 提供应用级别依赖的具体实现
 * 这种模式的优势：
 * 1. 简单直观，易于理解和调试
 * 2. 编译时间快，没有注解处理开销
 * 3. 可以与 Hilt 共存，处理不同层次的依赖
 */
class DefaultAppContainer(
    override val context: Context,
    dataStore: DataStore<Preferences>
) : AppContainer {
    
    override val dataStoreRepository: DataStoreRepository by lazy {
        DefaultDataStoreRepository(dataStore)
    }
    
    // 示例：主题管理器（如果需要的话）
    // val themeManager: ThemeManager by lazy {
    //     DefaultThemeManager(dataStoreRepository)
    // }
    
    // 示例：配置管理器
    // val configManager: ConfigManager by lazy {
    //     DefaultConfigManager(context, dataStoreRepository)
    // }
}

/**
 * 使用建议：
 * 
 * 1. 保持 Hilt 用于业务逻辑层的依赖注入
 * 2. 使用 AppContainer 管理应用级别的全局状态
 * 3. 在 Application 类中初始化 AppContainer
 * 4. 通过 Application 实例访问 AppContainer
 * 
 * 这样可以结合两种模式的优势：
 * - Hilt 处理复杂的业务依赖
 * - AppContainer 处理简单的全局依赖
 */
