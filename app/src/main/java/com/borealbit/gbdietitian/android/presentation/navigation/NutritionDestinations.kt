/*
 * Copyright 2025 BorealBit
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.borealbit.gbdietitian.android.presentation.navigation

import kotlinx.serialization.Serializable

/**
 * 营养师应用的导航目标定义
 * 
 * 学习自 EdgeMind 的类型安全路由系统
 * 使用 Kotlin Serialization 确保类型安全
 */

/**
 * 主要导航目标
 */
@Serializable
object HomeDestination {
    const val route = "home"
}

@Serializable
object ScanDestination {
    const val route = "scan"
}

@Serializable
object AnalysisDestination {
    const val route = "analysis"
}

@Serializable
object ProfileDestination {
    const val route = "profile"
}

/**
 * 功能性导航目标
 */
@Serializable
data class FoodDetailDestination(
    val foodId: String
) {
    companion object {
        const val route = "food_detail"
        const val routeWithArgs = "food_detail/{foodId}"
    }
}

@Serializable
data class NutritionLogDestination(
    val date: String? = null
) {
    companion object {
        const val route = "nutrition_log"
        const val routeWithArgs = "nutrition_log?date={date}"
    }
}

@Serializable
data class FoodSearchDestination(
    val query: String? = null
) {
    companion object {
        const val route = "food_search"
        const val routeWithArgs = "food_search?query={query}"
    }
}

@Serializable
object CameraDestination {
    const val route = "camera"
}

@Serializable
object SettingsDestination {
    const val route = "settings"
}

@Serializable
object GoalsDestination {
    const val route = "goals"
}

@Serializable
object HistoryDestination {
    const val route = "history"
}

@Serializable
object ReportsDestination {
    const val route = "reports"
}

/**
 * AI 功能导航目标
 */
@Serializable
data class AiChatDestination(
    val sessionId: String? = null
) {
    companion object {
        const val route = "ai_chat"
        const val routeWithArgs = "ai_chat?sessionId={sessionId}"
    }
}

@Serializable
data class NutritionAnalysisDestination(
    val foodId: String
) {
    companion object {
        const val route = "nutrition_analysis"
        const val routeWithArgs = "nutrition_analysis/{foodId}"
    }
}

@Serializable
object HealthConnectDestination {
    const val route = "health_connect"
}

/**
 * 导航参数键
 */
object NavigationArgs {
    const val FOOD_ID = "foodId"
    const val DATE = "date"
    const val QUERY = "query"
    const val SESSION_ID = "sessionId"
}

/**
 * 深度链接模式
 */
object DeepLinks {
    const val BASE_URL = "gbdietitian://nutrition"
    
    // 食物详情深度链接
    const val FOOD_DETAIL = "$BASE_URL/food/{foodId}"
    
    // 营养日志深度链接
    const val NUTRITION_LOG = "$BASE_URL/log/{date}"
    
    // AI 聊天深度链接
    const val AI_CHAT = "$BASE_URL/chat/{sessionId}"
    
    // 扫描功能深度链接
    const val SCAN = "$BASE_URL/scan"
    
    // 分析报告深度链接
    const val ANALYSIS = "$BASE_URL/analysis"
}

/**
 * 底部导航项目
 */
enum class BottomNavItem(
    val route: String,
    val titleRes: String,
    val iconRes: String
) {
    HOME(
        route = HomeDestination.route,
        titleRes = "首页",
        iconRes = "ic_home"
    ),
    SCAN(
        route = ScanDestination.route,
        titleRes = "扫描",
        iconRes = "ic_camera"
    ),
    ANALYSIS(
        route = AnalysisDestination.route,
        titleRes = "分析",
        iconRes = "ic_analytics"
    ),
    PROFILE(
        route = ProfileDestination.route,
        titleRes = "我的",
        iconRes = "ic_person"
    )
}

/**
 * 导航工具函数
 */
object NavigationUtils {
    
    /**
     * 构建带参数的路由
     */
    fun buildFoodDetailRoute(foodId: String): String {
        return "food_detail/$foodId"
    }
    
    fun buildNutritionLogRoute(date: String? = null): String {
        return if (date != null) {
            "nutrition_log?date=$date"
        } else {
            "nutrition_log"
        }
    }
    
    fun buildFoodSearchRoute(query: String? = null): String {
        return if (query != null) {
            "food_search?query=$query"
        } else {
            "food_search"
        }
    }
    
    fun buildAiChatRoute(sessionId: String? = null): String {
        return if (sessionId != null) {
            "ai_chat?sessionId=$sessionId"
        } else {
            "ai_chat"
        }
    }
    
    fun buildNutritionAnalysisRoute(foodId: String): String {
        return "nutrition_analysis/$foodId"
    }
    
    /**
     * 解析深度链接
     */
    fun parseDeepLink(url: String): String? {
        return when {
            url.startsWith(DeepLinks.FOOD_DETAIL) -> {
                val foodId = url.substringAfterLast("/")
                buildFoodDetailRoute(foodId)
            }
            url.startsWith(DeepLinks.NUTRITION_LOG) -> {
                val date = url.substringAfterLast("/")
                buildNutritionLogRoute(date)
            }
            url.startsWith(DeepLinks.AI_CHAT) -> {
                val sessionId = url.substringAfterLast("/")
                buildAiChatRoute(sessionId)
            }
            url == DeepLinks.SCAN -> ScanDestination.route
            url == DeepLinks.ANALYSIS -> AnalysisDestination.route
            else -> null
        }
    }
}

/**
 * 导航事件
 */
sealed class NavigationEvent {
    object NavigateUp : NavigationEvent()
    data class NavigateTo(val route: String) : NavigationEvent()
    data class NavigateToWithPopUp(val route: String, val popUpTo: String) : NavigationEvent()
    data class NavigateToFoodDetail(val foodId: String) : NavigationEvent()
    data class NavigateToNutritionLog(val date: String? = null) : NavigationEvent()
    data class NavigateToFoodSearch(val query: String? = null) : NavigationEvent()
    data class NavigateToAiChat(val sessionId: String? = null) : NavigationEvent()
    data class NavigateToNutritionAnalysis(val foodId: String) : NavigationEvent()
}
