/*
 * Copyright 2025 BorealBit
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.borealbit.gbdietitian.android.presentation.theme

import androidx.compose.runtime.mutableStateOf

/**
 * 主题设置管理
 * 
 * 学习自 EdgeMind 的主题管理模式
 * 提供全局的主题状态管理
 */

// 主题模式常量
const val THEME_SYSTEM = "system"
const val THEME_LIGHT = "light"
const val THEME_DARK = "dark"

/**
 * 全局主题设置
 * 学习自 EdgeMind 的 ThemeSettings 对象
 */
object ThemeSettings {
    /**
     * 主题覆盖设置
     * 可以是 THEME_SYSTEM, THEME_LIGHT, 或 THEME_DARK
     */
    val themeOverride = mutableStateOf(THEME_SYSTEM)
    
    /**
     * 是否启用动态颜色（Android 12+）
     */
    val dynamicColorEnabled = mutableStateOf(true)
    
    /**
     * 是否启用高对比度模式
     */
    val highContrastEnabled = mutableStateOf(false)
    
    /**
     * 设置主题模式
     */
    fun setThemeMode(mode: String) {
        themeOverride.value = mode
    }
    
    /**
     * 切换动态颜色
     */
    fun toggleDynamicColor() {
        dynamicColorEnabled.value = !dynamicColorEnabled.value
    }
    
    /**
     * 切换高对比度模式
     */
    fun toggleHighContrast() {
        highContrastEnabled.value = !highContrastEnabled.value
    }
    
    /**
     * 重置为默认设置
     */
    fun resetToDefaults() {
        themeOverride.value = THEME_SYSTEM
        dynamicColorEnabled.value = true
        highContrastEnabled.value = false
    }
}
