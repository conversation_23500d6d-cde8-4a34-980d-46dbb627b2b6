#!/bin/bash

# GuoBiaoDietitian Android App - Initial Commit Script
echo "🚀 Initializing Git repository for GuoBiaoDietitian Android App..."

# Initialize git repository
git init

# Configure git user
git config user.name "<PERSON>"
git config user.email "<EMAIL>"

# Add all files
echo "📁 Adding all files to git..."
git add .

# Check status
echo "📋 Git status:"
git status

# Create initial commit with detailed message
echo "💾 Creating initial commit..."
git commit -m "feat: initial project setup for GuoBiaoDietitian Android app

🏗️ Project Architecture:
- Clean Architecture (Data/Domain/Presentation layers)
- MVVM pattern with Jetpack Compose
- Hilt dependency injection
- Room database with migration support
- Kotlin Coroutines + Flow for reactive programming

🎨 UI Components:
- Material 3 design system
- Custom nutrition ring charts
- Food item cards with GB certification indicators
- Camera preview and barcode scanner components
- Loading, error, and empty state components

🤖 AI Integration Framework:
- Gemini Nano integration for nutrition chat
- ONNX Runtime for food recognition
- Extensible AI model manager interface
- Support for offline AI processing

📱 Core Features:
- Food recognition via camera and barcode scanning
- GB 28050-2011 nutrition label standard compliance
- Real-time nutrition tracking and analysis
- Health Connect API integration ready
- Personalized nutrition recommendations

🛠️ Technical Stack:
- Kotlin 1.9.20
- Jetpack Compose 2024.02.00
- Room 2.6.1
- CameraX 1.3.1
- Hilt 2.48
- Material 3 with dynamic theming

📦 Package Structure:
- com.borealbit.gbdietitian.android
- Modular architecture with clear separation of concerns
- Comprehensive error handling and state management

🔧 Build Configuration:
- Gradle Kotlin DSL
- ProGuard rules for AI models
- Health Connect permissions
- Camera and storage permissions

This initial setup provides a solid foundation for building
a comprehensive nutrition tracking app based on Chinese
dietary guidelines and GB standards."

echo "✅ Initial commit completed!"
echo "📊 Repository statistics:"
git log --oneline
echo ""
echo "📁 Files tracked:"
git ls-files | wc -l
echo " files total"
